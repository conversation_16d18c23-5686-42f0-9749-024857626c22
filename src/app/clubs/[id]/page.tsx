import { <PERSON>ada<PERSON> } from "next"
import { notFound } from "next/navigation"
import Link from "next/link"
import Image from "next/image"
import { ArrowLeft, Mail, Github, Linkedin, Users, Award } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"

// This would typically come from a database or CMS
const clubsData: Record<string, any> = {
  "metis": {
    name: "Metis, Development Club",
    description: "Focuses on software development and coding, where members work on real-world projects, contribute to open-source, and hone their programming skills.",
    longDescription: "Metis, the Development Club, is dedicated to fostering software development skills among students. We focus on real-world project development, open-source contributions, and modern programming practices. Our members work with various technologies including web development, mobile apps, and system programming to create innovative solutions.",
    category: "Software Development",
    members: "60+",
    established: "2018",
    email: "<EMAIL>",
    achievements: [
      "Contributed to 50+ open-source projects",
      "Developed campus management applications",
      "Winner - National Coding Competition 2023",
      "Published multiple software libraries"
    ],
    projects: [
      "Campus Event Management System",
      "Student Collaboration Platform",
      "Open Source Library Development",
      "Mobile App for Campus Services"
    ],
    team: [
      { name: "<PERSON><PERSON>", role: "Club President", email: "<EMAIL>" },
      { name: "<PERSON>riya <PERSON>", role: "Technical Lead", email: "<EMAIL>" },
      { name: "Rohit Gupta", role: "Open Source Coordinator", email: "<EMAIL>" },
      { name: "Sneha Patel", role: "Project Manager", email: "<EMAIL>" }
    ]
  },
  "digis": {
    name: "Digis, Digital Sports Club",
    description: "Combines technology with gaming, offering a platform for students interested in game development, e-sports, and the study of digital sports ecosystems.",
    longDescription: "Digis, the Digital Sports Club, bridges the gap between technology and gaming. We focus on game development, e-sports competitions, and understanding digital sports ecosystems. Our members explore game design, competitive gaming strategies, and the technological aspects of modern digital sports.",
    category: "Gaming & Technology",
    members: "45+",
    established: "2019",
    email: "<EMAIL>",
    achievements: [
      "Inter-IIT E-sports Championship Winners",
      "Developed 5+ indie games",
      "Organized National Gaming Tournament",
      "Partnership with gaming industry leaders"
    ],
    projects: [
      "Campus E-sports Tournament Platform",
      "Indie Game Development Projects",
      "Gaming Analytics Dashboard",
      "Virtual Reality Gaming Experience"
    ],
    team: [
      { name: "Karan Joshi", role: "Club President", email: "<EMAIL>" },
      { name: "Ananya Reddy", role: "Game Development Lead", email: "<EMAIL>" },
      { name: "Vikram Singh", role: "E-sports Coordinator", email: "<EMAIL>" },
      { name: "Riya Sharma", role: "Event Manager", email: "<EMAIL>" }
    ]
  },
  "mean-mechanics": {
    name: "Mean Mechanics, Robotics Club",
    description: "Specialises in designing and building robots, providing hands-on experience in robotics, automation, and mechatronics.",
    longDescription: "Mean Mechanics, our Robotics Club, specializes in designing and building cutting-edge robotic systems. We provide hands-on experience in robotics, automation, and mechatronics through practical projects and competitions. Our members work on autonomous systems, industrial automation, and innovative robotic solutions.",
    category: "Robotics & Automation",
    members: "55+",
    established: "2017",
    email: "<EMAIL>",
    achievements: [
      "Winner - Inter IIT Robotics Competition",
      "Developed autonomous campus delivery robot",
      "Best Innovation Award - National Robotics Meet",
      "Published research in robotics journals"
    ],
    projects: [
      "Autonomous Navigation Robot",
      "Industrial Automation System",
      "Swarm Robotics Research",
      "Robotic Arm for Manufacturing"
    ],
    team: [
      { name: "Aditya Kumar", role: "Club President", email: "<EMAIL>" },
      { name: "Meera Patel", role: "Hardware Lead", email: "<EMAIL>" },
      { name: "Rahul Verma", role: "Software Lead", email: "<EMAIL>" },
      { name: "Pooja Jain", role: "Research Coordinator", email: "<EMAIL>" }
    ]
  },
  "odyssey": {
    name: "Odyssey, Astronomy Club",
    description: "Explores the wonders of the universe, with activities ranging from stargazing sessions to discussions on astrophysics and space technology.",
    longDescription: "Odyssey, the Astronomy Club, is dedicated to exploring the wonders of the universe. We organize stargazing sessions, astrophysics discussions, and space technology workshops. Our members engage in astronomical observations, space mission analysis, and contribute to citizen science projects in astronomy.",
    category: "Space & Astronomy",
    members: "40+",
    established: "2016",
    email: "<EMAIL>",
    achievements: [
      "Discovered 3 asteroids through citizen science",
      "Organized National Astronomy Olympiad",
      "Built campus observatory telescope",
      "Collaboration with ISRO for student projects"
    ],
    projects: [
      "Campus Observatory Development",
      "Asteroid Discovery Program",
      "Space Mission Simulation",
      "Astrophotography Documentation"
    ],
    team: [
      { name: "Siddharth Joshi", role: "Club President", email: "<EMAIL>" },
      { name: "Kavya Sharma", role: "Observation Lead", email: "<EMAIL>" },
      { name: "Arjun Reddy", role: "Research Coordinator", email: "<EMAIL>" },
      { name: "Nisha Gupta", role: "Outreach Manager", email: "<EMAIL>" }
    ]
  },
  "grasp": {
    name: "GRASP, CP Club",
    description: "Dedicated to competitive programming, where members regularly participate in coding contests and work on improving their problem-solving abilities.",
    longDescription: "GRASP, the Competitive Programming Club, is dedicated to enhancing algorithmic thinking and problem-solving skills. We regularly participate in coding contests, organize training sessions, and help students excel in competitive programming platforms like Codeforces, CodeChef, and AtCoder.",
    category: "Competitive Programming",
    members: "70+",
    established: "2015",
    email: "<EMAIL>",
    achievements: [
      "ACM ICPC World Finals Qualification",
      "Multiple Codeforces Grandmaster ratings",
      "Winner - Google Code Jam India",
      "Top performers in major coding contests"
    ],
    projects: [
      "Online Judge Platform Development",
      "Algorithm Visualization Tools",
      "Contest Management System",
      "Competitive Programming Training Portal"
    ],
    team: [
      { name: "Harsh Agarwal", role: "Club President", email: "<EMAIL>" },
      { name: "Divya Singh", role: "Training Coordinator", email: "<EMAIL>" },
      { name: "Rohan Patel", role: "Contest Manager", email: "<EMAIL>" },
      { name: "Ankit Sharma", role: "Mentorship Lead", email: "<EMAIL>" }
    ]
  },
  "machine-learning": {
    name: "Machine Learning Club",
    description: "Focuses on machine learning and AI, offering a space for students to experiment with algorithms, data science projects, and cutting-edge research.",
    longDescription: "The Machine Learning Club focuses on advancing artificial intelligence and machine learning research. We provide a platform for students to experiment with algorithms, work on data science projects, and contribute to cutting-edge AI research. Our members explore deep learning, computer vision, NLP, and other AI domains.",
    category: "Artificial Intelligence",
    members: "80+",
    established: "2018",
    email: "<EMAIL>",
    achievements: [
      "Published 15+ research papers in AI conferences",
      "Winner - National AI Challenge 2023",
      "Collaboration with industry AI labs",
      "Developed AI solutions for social good"
    ],
    projects: [
      "Computer Vision for Healthcare",
      "Natural Language Processing Research",
      "Reinforcement Learning Applications",
      "AI Ethics and Fairness Studies"
    ],
    team: [
      { name: "Priyanka Reddy", role: "Club President", email: "<EMAIL>" },
      { name: "Amit Joshi", role: "Research Lead", email: "<EMAIL>" },
      { name: "Neha Gupta", role: "Project Coordinator", email: "<EMAIL>" },
      { name: "Vikash Kumar", role: "Industry Liaison", email: "<EMAIL>" }
    ]
  },
  "tinkerers-lab": {
    name: "TINKERER'S LAB",
    description: "A hands-on innovation space where students experiment with hardware, prototyping, and creative engineering solutions to bring ideas to life.",
    longDescription: "TINKERER'S LAB is a hands-on innovation space dedicated to experimental engineering and creative problem-solving. We provide access to advanced prototyping tools, 3D printers, and fabrication equipment. Our members work on interdisciplinary projects, hardware innovations, and creative engineering solutions.",
    category: "Innovation & Prototyping",
    members: "50+",
    established: "2020",
    email: "<EMAIL>",
    achievements: [
      "Developed 20+ innovative prototypes",
      "Winner - National Innovation Challenge",
      "Patent applications for student inventions",
      "Collaboration with startup incubators"
    ],
    projects: [
      "Smart Campus Infrastructure Solutions",
      "Sustainable Technology Prototypes",
      "IoT Device Development",
      "3D Printing Innovation Projects"
    ],
    team: [
      { name: "Rajesh Sharma", role: "Lab Coordinator", email: "<EMAIL>" },
      { name: "Kavya Patel", role: "Hardware Lead", email: "<EMAIL>" },
      { name: "Arjun Singh", role: "Fabrication Specialist", email: "<EMAIL>" },
      { name: "Riya Joshi", role: "Innovation Manager", email: "<EMAIL>" }
    ]
  },
  "anveshanam": {
    name: "Anveshanam",
    description: "Research and innovation club focused on exploring cutting-edge technologies and conducting academic research projects.",
    longDescription: "Anveshanam is a research and innovation club dedicated to exploring cutting-edge technologies and conducting high-quality academic research. We foster a culture of scientific inquiry and innovation, encouraging students to pursue research projects across various domains of technology and engineering. Our members engage in collaborative research, publish papers, and contribute to the advancement of knowledge.",
    category: "Research & Innovation",
    members: "30+",
    established: "2019",
    email: "<EMAIL>",
    achievements: [
      "Published 10+ research papers in peer-reviewed journals",
      "Winner - National Research Innovation Challenge",
      "Collaboration with leading research institutions",
      "Patent applications for innovative technologies"
    ],
    projects: [
      "Interdisciplinary Research Initiatives",
      "Technology Innovation Projects",
      "Academic Paper Publication Program",
      "Research Collaboration Platform"
    ],
    team: [
      { name: "Dr. Priya Sharma", role: "Faculty Advisor", email: "<EMAIL>" },
      { name: "Arjun Patel", role: "Club President", email: "<EMAIL>" },
      { name: "Sneha Reddy", role: "Research Coordinator", email: "<EMAIL>" },
      { name: "Kiran Joshi", role: "Publication Lead", email: "<EMAIL>" }
    ]
  },
  "systems": {
    name: "Systems Club",
    description: "Delves into the intricacies of computer systems, including networking, and hardware-software integration.",
    longDescription: "The Systems Club delves deep into the intricacies of computer systems, exploring networking protocols, operating systems, and hardware-software integration. We focus on understanding how complex systems work together and develop expertise in system administration, network security, and distributed systems.",
    category: "Computer Systems",
    members: "35+",
    established: "2019",
    email: "<EMAIL>",
    achievements: [
      "Built campus network monitoring system",
      "Contributed to open-source OS projects",
      "Winner - National Systems Programming Contest",
      "Developed high-performance computing solutions"
    ],
    projects: [
      "Campus Network Infrastructure Analysis",
      "Distributed Computing Framework",
      "System Performance Optimization",
      "Network Security Implementation"
    ],
    team: [
      { name: "Karthik Reddy", role: "Group Coordinator", email: "<EMAIL>" },
      { name: "Anjali Sharma", role: "Network Specialist", email: "<EMAIL>" },
      { name: "Rohit Gupta", role: "Systems Administrator", email: "<EMAIL>" },
      { name: "Priya Jain", role: "Security Lead", email: "<EMAIL>" }
    ]
  },
  "embed": {
    name: "Embed Club",
    description: "Concentrates on embedded systems, where students design and develop microcontroller-based projects, learning about hardware programming and IoT applications.",
    longDescription: "The Embed Club concentrates on embedded systems development, where students design and develop microcontroller-based projects. We focus on hardware programming, IoT applications, and real-time systems. Our members work with various microcontrollers, sensors, and communication protocols to create innovative embedded solutions.",
    category: "Embedded Systems",
    members: "40+",
    established: "2018",
    email: "<EMAIL>",
    achievements: [
      "Developed smart campus IoT infrastructure",
      "Winner - National Embedded Systems Competition",
      "Published embedded systems research",
      "Industry collaboration for IoT projects"
    ],
    projects: [
      "Smart Campus Monitoring System",
      "Wearable Health Monitoring Device",
      "Autonomous Vehicle Control System",
      "Industrial IoT Solutions"
    ],
    team: [
      { name: "Suresh Kumar", role: "Group Coordinator", email: "<EMAIL>" },
      { name: "Deepika Patel", role: "Hardware Lead", email: "<EMAIL>" },
      { name: "Manish Singh", role: "Firmware Developer", email: "<EMAIL>" },
      { name: "Ritu Sharma", role: "IoT Specialist", email: "<EMAIL>" }
    ]
  },
  "twist-theory": {
    name: "Twist Theory, Speed Cubing Hobby Group",
    description: "Dedicated to speedcubing, where members solve twisty puzzles competitively, learn advanced algorithms and how they work. We host workshops, and official WCA competitions for recognized results and a competitive experience.",
    longDescription: "Twist Theory is dedicated to the art and science of speedcubing. Our members solve twisty puzzles competitively, learn advanced algorithms, and understand the mathematical principles behind cube solving. We host workshops, training sessions, and official WCA competitions to provide recognized results and competitive experiences.",
    category: "Puzzle Solving",
    members: "25+",
    established: "2020",
    email: "<EMAIL>",
    achievements: [
      "Organized official WCA competition",
      "Multiple national record holders",
      "Developed algorithm learning platform",
      "International speedcubing recognition"
    ],
    projects: [
      "Algorithm Visualization Tool",
      "Speedcubing Training Platform",
      "Competition Management System",
      "Puzzle Solving Research"
    ],
    team: [
      { name: "Aarav Joshi", role: "Group Coordinator", email: "<EMAIL>" },
      { name: "Ishita Sharma", role: "Competition Manager", email: "<EMAIL>" },
      { name: "Aryan Patel", role: "Training Lead", email: "<EMAIL>" },
      { name: "Kavya Singh", role: "Algorithm Specialist", email: "<EMAIL>" }
    ]
  },
  "cybersentinel": {
    name: "CyberSentinel",
    description: "CyberSentinel is a cybersecurity hobby group where we dive into the world of ethical hacking, digital forensics, and cyber defense. From mastering tools like Wireshark and Burp Suite to capturing packets, analyzing exploits, and cracking CTF challenges — we break, build, and secure systems together.",
    longDescription: "CyberSentinel is a cybersecurity hobby group dedicated to ethical hacking, digital forensics, and cyber defense. We explore the fascinating world of cybersecurity through hands-on learning with industry-standard tools like Wireshark, Burp Suite, and Metasploit. Our members participate in CTF competitions, vulnerability research, and security awareness initiatives.",
    category: "Cybersecurity",
    members: "30+",
    established: "2021",
    email: "<EMAIL>",
    achievements: [
      "Winner - National CTF Championship",
      "Discovered security vulnerabilities in campus systems",
      "Organized cybersecurity awareness workshops",
      "Collaboration with cybersecurity firms"
    ],
    projects: [
      "Campus Security Assessment",
      "CTF Platform Development",
      "Vulnerability Research Program",
      "Security Awareness Campaign"
    ],
    team: [
      { name: "Aditya Verma", role: "Group Coordinator", email: "<EMAIL>" },
      { name: "Shreya Patel", role: "Penetration Testing Lead", email: "<EMAIL>" },
      { name: "Kiran Joshi", role: "Digital Forensics Specialist", email: "<EMAIL>" },
      { name: "Rahul Singh", role: "CTF Coordinator", email: "<EMAIL>" }
    ]
  },
  "lambda": {
    name: "Lambda",
    description: "LAMBDA focuses on programming languages, their theory and implementation. We try to be active in participating in both research and hands-on projects.",
    longDescription: "Lambda focuses on the theoretical and practical aspects of programming languages. We explore language design, compiler construction, type theory, and functional programming paradigms. Our group participates in both academic research and hands-on projects to understand how programming languages work and evolve.",
    category: "Programming Languages",
    members: "20+",
    established: "2020",
    email: "<EMAIL>",
    achievements: [
      "Developed experimental programming language",
      "Published research on type systems",
      "Contributed to open-source compilers",
      "Organized programming language workshops"
    ],
    projects: [
      "Functional Programming Language Design",
      "Compiler Optimization Research",
      "Type System Implementation",
      "Language Interoperability Studies"
    ],
    team: [
      { name: "Varun Agarwal", role: "Group Coordinator", email: "<EMAIL>" },
      { name: "Nisha Reddy", role: "Research Lead", email: "<EMAIL>" },
      { name: "Siddharth Gupta", role: "Compiler Specialist", email: "<EMAIL>" },
      { name: "Ananya Jain", role: "Theory Researcher", email: "<EMAIL>" }
    ]
  },
  "blockchain-hobby": {
    name: "Blockchain Hobby Group",
    description: "Blockchain hobby group in our college to explore the fundamentals and real-world applications of blockchain technology. The group will meet regularly for hands-on sessions, discussions, and project-building. No prior experience needed—just curiosity and commitment!",
    longDescription: "The Blockchain Hobby Group is dedicated to exploring the fundamentals and real-world applications of blockchain technology. We meet regularly for hands-on sessions, technical discussions, and collaborative project-building. Our group welcomes beginners and experts alike, focusing on practical learning and innovation in decentralized technologies.",
    category: "Blockchain Technology",
    members: "35+",
    established: "2021",
    email: "<EMAIL>",
    achievements: [
      "Developed decentralized applications (DApps)",
      "Organized blockchain hackathon",
      "Smart contract security research",
      "Partnership with blockchain startups"
    ],
    projects: [
      "Campus Cryptocurrency System",
      "Supply Chain Transparency Platform",
      "Decentralized Voting System",
      "NFT Marketplace for Student Art"
    ],
    team: [
      { name: "Rohan Sharma", role: "Group Coordinator", email: "<EMAIL>" },
      { name: "Priya Agarwal", role: "Smart Contract Developer", email: "<EMAIL>" },
      { name: "Amit Reddy", role: "DApp Developer", email: "<EMAIL>" },
      { name: "Sneha Joshi", role: "Research Coordinator", email: "<EMAIL>" }
    ]
  }
}

// Helper function to get logo path
const getLogoPath = (id: string) => {
  const logoMap: Record<string, string> = {
    // Technical Clubs
    'metis': '/logos/clubs/metis.jpeg',
    'digis': '/logos/clubs/digis.jpg',
    'mean-mechanics': '/logos/clubs/mean-mechanics.png',
    'odyssey': '/logos/clubs/odyssey.jpg',
    'grasp': '/logos/clubs/grasp.png',
    'machine-learning': '/logos/clubs/machine-learning.jpeg',
    'tinkerers-lab': '/logos/clubs/tinkerers-lab.png',
    'anveshanam': '/logos/clubs/anveshanam.png',

    // Hobby Groups
    'embed': '/logos/hobby-groups/embed.png',
    'blockchain-hobby': '/logos/hobby-groups/blockchain-hobby.png',
  }

  return logoMap[id] || null
}

interface PageProps {
  params: Promise<{
    id: string
  }>
}

export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const resolvedParams = await params;
  const club = clubsData[resolvedParams.id]

  if (!club) {
    return {
      title: "Club Not Found",
    }
  }

  return {
    title: `${club.name} - Technical Council IITGN`,
    description: club.description,
  }
}

export default async function ClubDetailPage({ params }: PageProps) {
  const resolvedParams = await params;
  const club = clubsData[resolvedParams.id]

  if (!club) {
    notFound()
  }

  return (
    <div className="flex flex-col">
      {/* Back Navigation */}
      <div className="container px-4 md:px-6 py-4">
        <Button asChild variant="ghost" size="sm" className="text-muted-foreground hover:text-foreground transition-colors">
          <Link href="/clubs">
            <ArrowLeft className="mr-1.5 h-3.5 w-3.5" />
            <span className="text-sm">Back to Clubs & Groups</span>
          </Link>
        </Button>
      </div>

      {/* Hero Section */}
      <section className="relative py-16 lg:py-24">
        <div className="absolute inset-0 gradient-bg opacity-10" />
        <div className="container relative z-10 px-4 md:px-6">
          <div className="grid gap-8 lg:grid-cols-2 lg:gap-16 items-center">
            <div className="space-y-6">
              <div className="space-y-2">
                <span className="inline-block rounded-full bg-blue-100 dark:bg-blue-900 px-3 py-1 text-sm font-medium text-blue-600">
                  {club.category}
                </span>
                <h1 className="text-4xl font-bold tracking-tighter sm:text-5xl font-space-grotesk">
                  {club.name}
                </h1>
                <p className="text-xl text-muted-foreground">
                  {club.description}
                </p>
              </div>

              <div className="flex flex-wrap gap-4 text-sm text-muted-foreground">
                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4" />
                  {club.members} members
                </div>
              </div>
            </div>

            <div className="aspect-square rounded-lg bg-gradient-to-br from-blue-600/20 to-purple-600/20 flex items-center justify-center overflow-hidden">
              {getLogoPath(resolvedParams.id) ? (
                <Image
                  src={getLogoPath(resolvedParams.id)!}
                  alt={`${club.name} logo`}
                  width={300}
                  height={300}
                  className="w-full h-full object-contain rounded-lg"
                />
              ) : (
                <div className="text-6xl font-bold text-muted-foreground opacity-50">
                  {club.name.split(' ').map((word: string) => word[0]).join('')}
                </div>
              )}
            </div>
          </div>
        </div>
      </section>

      {/* About Section */}
      <section className="py-16 bg-muted/50">
        <div className="container px-4 md:px-6">
          <div className="max-w-3xl">
            <h2 className="text-3xl font-bold tracking-tighter mb-6">About Us</h2>
            <p className="text-muted-foreground text-lg leading-relaxed">
              {club.longDescription}
            </p>
          </div>
        </div>
      </section>

      {/* Achievements & Projects */}
      <section className="py-16">
        <div className="container px-4 md:px-6">
          <div className="grid gap-12 lg:grid-cols-2">
            {/* Achievements */}
            <div className="space-y-6">
              <div className="flex items-center gap-2">
                <Award className="h-6 w-6 text-yellow-600" />
                <h3 className="text-2xl font-bold">Achievements</h3>
              </div>
              <ul className="space-y-3">
                {club.achievements.map((achievement: string, index: number) => (
                  <li key={index} className="flex items-start gap-3">
                    <div className="h-2 w-2 rounded-full bg-blue-600 mt-2 flex-shrink-0" />
                    <span className="text-muted-foreground">{achievement}</span>
                  </li>
                ))}
              </ul>
            </div>

            {/* Projects */}
            <div className="space-y-6">
              <h3 className="text-2xl font-bold">Current Projects</h3>
              <ul className="space-y-3">
                {club.projects.map((project: string, index: number) => (
                  <li key={index} className="flex items-start gap-3">
                    <div className="h-2 w-2 rounded-full bg-purple-600 mt-2 flex-shrink-0" />
                    <span className="text-muted-foreground">{project}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Team Members */}
      <section className="py-16 bg-muted/50">
        <div className="container px-4 md:px-6">
          <h3 className="text-2xl font-bold mb-8">Team Members</h3>
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {club.team.map((member: any, index: number) => (
              <div key={index} className="glass rounded-lg p-6 text-center">
                <div className="h-16 w-16 rounded-full bg-gradient-to-r from-blue-600 to-purple-600 mx-auto mb-4 flex items-center justify-center text-white font-bold text-lg">
                  {member.name.split(' ').map((n: string) => n[0]).join('')}
                </div>
                <h4 className="font-semibold">{member.name}</h4>
                <p className="text-sm text-muted-foreground mb-3">{member.role}</p>
                <Button asChild variant="outline" size="sm">
                  <a href={`mailto:${member.email}`}>
                    <Mail className="mr-2 h-3 w-3" />
                    Contact
                  </a>
                </Button>
              </div>
            ))}
          </div>
        </div>
      </section>


    </div>
  )
}
